import torch
import torch.nn as nn
import argparse
import subprocess
import numpy as np
import os

from pathlib import Path
from swarm_rl.sim2real.sim2real import load_sf_model


def compare_torch_to_c_model_outputs_single_drone():
    # set this to whatever your project path is
    project_root = Path.home().joinpath('quad-swarm-rl')
    os.chdir(str(project_root))
    # SF torch model used to generate the c model. Set this to be the dir where you store the torch model
    # you used to generate the c model
    torch_model_dir = 'swarm_rl/sim2real/torch_models/single'
    model = load_sf_model(Path(torch_model_dir), model_type='single')

    # get the pytorch model outputs on a random input observation. You can also set this to be some custom observation
    # if you want to debug a specific observation input
    obs = torch.randn((1, 18))
    obs_dict = {'obs': obs}
    torch_model_out = model.action_parameterization(model.actor_encoder(obs_dict))[1].means.detach().numpy()

    # get the c model outputs on the same observation

    # set this to be the directory where you store the c model generated by the torch model
    c_model_dir = Path('swarm_rl/sim2real/c_models/single')
    c_model_path = c_model_dir.joinpath('model.c')
    shared_lib_path = c_model_dir.joinpath('single.so')
    subprocess.run(
        ['g++', '-fPIC', '-shared', '-o', str(shared_lib_path), str(c_model_path)],
        check=True,
        stderr=subprocess.PIPE,
        stdout=subprocess.PIPE
    )

    import ctypes
    from numpy.ctypeslib import ndpointer
    lib = ctypes.cdll.LoadLibrary(str(shared_lib_path))
    func = lib.main
    func.restype = None
    func.argtypes = [
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
        ctypes.c_size_t,
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS")
    ]

    indata = obs.flatten().detach().numpy()
    outdata = np.zeros(4).astype(np.float32)
    func(indata, indata.size, outdata)

    assert np.allclose(torch_model_out, outdata)


def compare_torch_to_c_model_multi_drone_attention():
    project_root = Path.home().joinpath('quad-swarm-rl')
    os.chdir(str(project_root))

    # prepare the c model and main method for evaluation
    c_model_dir = Path('swarm_rl/sim2real/c_models/attention')
    c_model_path = c_model_dir.joinpath('model.c')
    shared_lib_path = c_model_dir.joinpath('multi_attn.so')
    subprocess.run(
        ['g++', '-fPIC', '-shared', '-o', str(shared_lib_path), str(c_model_path)],
        check=True,
        stderr=subprocess.PIPE,
        stdout=subprocess.PIPE
    )

    import ctypes
    from numpy.ctypeslib import ndpointer
    lib = ctypes.cdll.LoadLibrary(str(shared_lib_path))
    func = lib.main
    func.restype = None
    func.argtypes = [
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
        ndpointer(ctypes.c_float, flags="C_CONTIGUOUS"),
    ]

    torch_model_dir = 'swarm_rl/sim2real/torch_models/attention/'
    model = load_sf_model(Path(torch_model_dir), model_type='attention')

    # test 1000 times on different random inputs
    for _ in range(1000):
        # check the neighbor encoder outputs
        num_neighbors = 2
        neighbor_obs = torch.randn(36)
        torch_nbr_out = model.actor_encoder.neighbor_embed_layer(neighbor_obs).detach().numpy()
        nbr_indata = neighbor_obs.detach().numpy()
        nbr_outdata = np.zeros(16).astype(np.float32)

        # check the obstacle encoder outputs
        obstacle_obs = torch.rand(9)
        torch_obstacle_out = model.actor_encoder.obstacle_embed_layer(obstacle_obs).detach().numpy()
        obst_indata = obstacle_obs.detach().numpy()
        obst_outdata = np.zeros(16).astype(np.float32)  # TODO: make this cfg.rnn_size instead of hardcoded

        # check attention layer
        attn_input = torch.from_numpy(np.vstack((torch_nbr_out, torch_obstacle_out)))
        torch_attn_output, _ = model.actor_encoder.attention_layer(attn_input, attn_input, attn_input)
        # torch_attn_output = model.actor_encoder.attention_layer.softmax_out.detach().numpy()
        torch_attn_output = torch_attn_output.detach().numpy()
        token1_out = np.zeros(16).astype(np.float32)
        token2_out = np.zeros(16).astype(np.float32)

        self_obs = torch.randn(19)
        self_indata = self_obs.detach().numpy()
        obs_dict = {'obs': torch.concat([self_obs, neighbor_obs, obstacle_obs]).view(1, -1)}
        torch_thrust_out = model.action_parameterization(model.actor_encoder(obs_dict))[1].means.flatten().detach().numpy()
        thrust_out = np.zeros(4).astype(np.float32)

        func(self_indata, nbr_indata, obst_indata, nbr_outdata, obst_outdata, token1_out, token2_out, thrust_out)

        tokens = np.vstack((token1_out, token2_out))
        assert np.allclose(torch_obstacle_out, obst_outdata, atol=1e-6)
        assert np.allclose(torch_nbr_out, nbr_outdata, atol=1e-6)
        assert np.allclose(torch_attn_output, tokens, atol=1e-6)
        assert np.allclose(torch_thrust_out, thrust_out, atol=1e-6)


if __name__ == '__main__':
    compare_torch_to_c_model_multi_drone_attention()
    print('Pass Unit Test!')
