import numpy as np

from gym_art.quadrotor_multi.scenarios.utils import get_z_value
from gym_art.quadrotor_multi.scenarios.base import QuadrotorScenario


class Scenario_dynamic_diff_goal_fire_avoidance(QuadrotorScenario):
    def __init__(self, quads_mode, envs, num_agents, room_dims):
        super().__init__(quads_mode, envs, num_agents, room_dims)
        # teleport every [4.0, 6.0] secs
        duration_time = 5.0
        self.control_step_for_sec = int(duration_time * self.envs[0].control_freq)
        
        # Fire zones (red circles) parameters
        self.fire_zones = []
        self.fire_zone_radius = 1.5  # radius of fire zones
        self.num_fire_zones = 3  # number of fire zones
        self.fire_zone_penalty = -10.0  # penalty for entering fire zones
        self.fire_zone_detection_radius = 0.5  # detection radius around drone
        
        # Initialize fire zones
        self.generate_fire_zones()

    def generate_fire_zones(self):
        """Generate random fire zones in the environment"""
        self.fire_zones = []
        box_size = self.envs[0].box
        
        for _ in range(self.num_fire_zones):
            # Generate random position for fire zone
            x = np.random.uniform(low=-box_size * 0.8, high=box_size * 0.8)
            y = np.random.uniform(low=-box_size * 0.8, high=box_size * 0.8)
            z = np.random.uniform(low=0.5, high=3.0)
            
            fire_zone = {
                'center': np.array([x, y, z]),
                'radius': self.fire_zone_radius
            }
            self.fire_zones.append(fire_zone)

    def check_fire_zone_collision(self, drone_pos):
        """Check if drone is in any fire zone"""
        for fire_zone in self.fire_zones:
            distance = np.linalg.norm(drone_pos - fire_zone['center'])
            if distance <= (fire_zone['radius'] + self.fire_zone_detection_radius):
                return True
        return False

    def get_fire_zone_penalty(self, drone_positions):
        """Calculate penalty for drones in fire zones"""
        penalties = np.zeros(len(drone_positions))
        for i, pos in enumerate(drone_positions):
            if self.check_fire_zone_collision(pos):
                penalties[i] = self.fire_zone_penalty
        return penalties

    def update_goals(self):
        # Reset formation and related parameters
        self.update_formation_and_relate_param()

        # Reset goals
        self.goals = self.generate_goals(num_agents=self.num_agents, formation_center=self.formation_center,
                                         layer_dist=self.layer_dist)
        np.random.shuffle(self.goals)

    def step(self):
        tick = self.envs[0].tick
        if tick % self.control_step_for_sec == 0 and tick > 0:
            box_size = self.envs[0].box
            x, y = np.random.uniform(low=-box_size, high=box_size, size=(2,))

            # Get z value, and make sure all goals will above the ground
            z = get_z_value(num_agents=self.num_agents, num_agents_per_layer=self.num_agents_per_layer,
                            box_size=box_size, formation=self.formation, formation_size=self.formation_size)

            self.formation_center = np.array([x, y, z])
            self.update_goals()

            # Update goals to envs
            for i, env in enumerate(self.envs):
                env.goal = self.goals[i]

        return

    def reset(self):
        # Update duration time
        duration_time = np.random.uniform(low=4.0, high=6.0)
        self.control_step_for_sec = int(duration_time * self.envs[0].control_freq)

        # Generate new fire zones
        self.generate_fire_zones()

        # Reset formation, and parameters related to the formation; formation center; goals
        self.standard_reset()
