import plotly.express as px
import numpy as np

"""
Check plot_v_value_2d.py for more details. But code you should use below
tmp_score=[]
for i in range(-10, 11):
    ti_score = []
    for j in range(-10, 11):
        normalized_obs_dict['obs'][0][0]=i * 0.2
        normalized_obs_dict['obs'][0][1]=j * 0.2
        x = self.forward_head(normalized_obs_dict)
        x, new_rnn_states = self.forward_core(x, rnn_states)
        result = self.forward_tail(x, values_only, sample_actions=True)
        ti_score.append(result['values'].item())            
    tmp_score.append(ti_score)
print(tmp_score)
"""

x = np.arange(-2.0, 2.1, 0.2)
y = np.arange(-2.0, 2.1, 0.2)
value = np.array([[-1.046976089477539, -0.9372676014900208, -0.834980845451355, -0.747315526008606, -0.6754142642021179, -0.6204982995986938, -0.5861996412277222, -0.5694772005081177, -0.5638792514801025, -0.5645636916160583, -0.5689689517021179, -0.5766781568527222, -0.5895943641662598, -0.6124382019042969, -0.6521246433258057, -0.7118673324584961, -0.7814335823059082, -0.8448623418807983, -0.8992334008216858, -0.9481641054153442, -0.9929596781730652], [-1.001110315322876, -0.8912216424942017, -0.7898903489112854, -0.7035033702850342, -0.6336020231246948, -0.5800396800041199, -0.5459231734275818, -0.5294400453567505, -0.5243607759475708, -0.5255995988845825, -0.5304893255233765, -0.5386357307434082, -0.5520575046539307, -0.5757855176925659, -0.6171807050704956, -0.67908775806427, -0.7503697276115417, -0.8163374662399292, -0.8733943700790405, -0.9221827983856201, -0.9648914337158203], [-0.965925395488739, -0.856200098991394, -0.7552466988563538, -0.6677525639533997, -0.5958312153816223, -0.5406490564346313, -0.5056169033050537, -0.48912134766578674, -0.48440036177635193, -0.4859941899776459, -0.49122652411460876, -0.4998341500759125, -0.5141236782073975, -0.5397152304649353, -0.5846407413482666, -0.6513938903808594, -0.727941632270813, -0.7989629507064819, -0.8578280210494995, -0.9047396779060364, -0.9450585246086121], [-0.9437981247901917, -0.8353913426399231, -0.735020101070404, -0.6454590559005737, -0.5682094097137451, -0.5068633556365967, -0.46816954016685486, -0.45107850432395935, -0.4466775357723236, -0.448301762342453, -0.45332011580467224, -0.46189138293266296, -0.47699663043022156, -0.5052996873855591, -0.555709958076477, -0.6299858093261719, -0.7139055132865906, -0.7888553142547607, -0.8466383218765259, -0.8906763792037964, -0.9293955564498901], [-0.9321250915527344, -0.8279315233230591, -0.7301548719406128, -0.6398402452468872, -0.5568625926971436, -0.48590990900993347, -0.4391673505306244, -0.4193834960460663, -0.4150422513484955, -0.41663965582847595, -0.4211500585079193, -0.4294341504573822, -0.445555180311203, -0.47743186354637146, -0.5345587730407715, -0.6169990301132202, -0.7062991261482239, -0.7808215618133545, -0.8351883292198181, -0.8767697215080261, -0.9152470827102661], [-0.9265629053115845, -0.8304659724235535, -0.73865807056427, -0.6505098342895508, -0.5639863014221191, -0.4832795560359955, -0.42511722445487976, -0.3989350497722626, -0.39315328001976013, -0.3946426808834076, -0.3989144265651703, -0.4073300063610077, -0.4251159131526947, -0.4612562358379364, -0.5250779390335083, -0.6132701635360718, -0.7024236917495728, -0.7721246480941772, -0.8222391605377197, -0.8622326850891113, -0.9015994071960449], [-0.9232174754142761, -0.838615357875824, -0.7561361193656921, -0.6733987927436829, -0.5865296125411987, -0.49853846430778503, -0.428703635931015, -0.3930003345012665, -0.3830232322216034, -0.3834511935710907, -0.38759228587150574, -0.39668580889701843, -0.4168696105480194, -0.4578286111354828, -0.5274792909622192, -0.6171200275421143, -0.7005724906921387, -0.762961208820343, -0.8086904287338257, -0.8475642204284668, -0.8883485198020935], [-0.9187061190605164, -0.8470710515975952, -0.7755028605461121, -0.6999533772468567, -0.6152247190475464, -0.5232142210006714, -0.4439748227596283, -0.3980672061443329, -0.3817845284938812, -0.3799963891506195, -0.3840617835521698, -0.3945504128932953, -0.4181308448314667, -0.4646209180355072, -0.5387508869171143, -0.625693678855896, -0.7003771066665649, -0.754810094833374, -0.7961693406105042, -0.8338682651519775, -0.8759613633155823], [-0.9099370837211609, -0.8503724336624146, -0.7890582084655762, -0.7205069065093994, -0.6388754844665527, -0.5453122854232788, -0.45945867896080017, -0.40464839339256287, -0.3818751275539398, -0.3779023587703705, -0.3829263150691986, -0.39646169543266296, -0.42514440417289734, -0.47791406512260437, -0.5546737909317017, -0.6361304521560669, -0.7015547752380371, -0.7487059831619263, -0.7860360145568848, -0.822424054145813, -0.8653624057769775], [-0.8946007490158081, -0.844650387763977, -0.7918769121170044, -0.7294962406158447, -0.6510840654373169, -0.5571215152740479, -0.4664318263530731, -0.4042663276195526, -0.37589308619499207, -0.3708593547344208, -0.3789813220500946, -0.3982878625392914, -0.4344136416912079, -0.49386510252952576, -0.5711605548858643, -0.6459023952484131, -0.7030932903289795, -0.7443174123764038, -0.7783995866775513, -0.8136984705924988, -0.8571405410766602], [-0.8724964261054993, -0.8291149735450745, -0.7827274799346924, -0.7255319952964783, -0.650039792060852, -0.555810809135437, -0.4609696567058563, -0.3924393355846405, -0.3593740165233612, -0.35463187098503113, -0.36839625239372253, -0.3967157304286957, -0.4427048861980438, -0.5086791515350342, -0.5848443508148193, -0.6530541181564331, -0.7037041187286377, -0.74048912525177, -0.7722121477127075, -0.8068337440490723, -0.8506304025650024], [-0.8474622368812561, -0.8074976801872253, -0.7646045684814453, -0.7106692790985107, -0.636985182762146, -0.5418597459793091, -0.44290849566459656, -0.3685579001903534, -0.3314175307750702, -0.3278977572917938, -0.3493361175060272, -0.38948854804039, -0.44707390666007996, -0.518964409828186, -0.5933539867401123, -0.6563674211502075, -0.7025274038314819, -0.73633873462677, -0.7664185762405396, -0.8005514144897461, -0.8443855047225952], [-0.8268623352050781, -0.7877472639083862, -0.7454277873039246, -0.6920167803764343, -0.618085503578186, -0.5207589864730835, -0.4172752797603607, -0.33730027079582214, -0.29638370871543884, -0.2942673861980438, -0.3240102231502533, -0.377157062292099, -0.4464816749095917, -0.5232994556427002, -0.5961830615997314, -0.6559625864028931, -0.6998952627182007, -0.732306957244873, -0.7613352537155151, -0.794771671295166, -0.83785080909729], [-0.8164584040641785, -0.7768016457557678, -0.7334421277046204, -0.6786558628082275, -0.6026935577392578, -0.5019258260726929, -0.3934774100780487, -0.3080500066280365, -0.26367780566215515, -0.26283368468284607, -0.30020174384117126, -0.36524030566215515, -0.44431373476982117, -0.5243933200836182, -0.5960735082626343, -0.6543233394622803, -0.6981136798858643, -0.7309390306472778, -0.759813666343689, -0.7922621965408325, -0.8333615064620972], [-0.8173725008964539, -0.776611864566803, -0.7318060398101807, -0.6752077341079712, -0.5967593193054199, -0.492302268743515, -0.3790836036205292, -0.2887987792491913, -0.24171744287014008, -0.24246035516262054, -0.2865600287914276, -0.36096176505088806, -0.446338027715683, -0.5274780988693237, -0.5977650880813599, -0.6554033756256104, -0.7005139589309692, -0.7356351613998413, -0.7660303115844727, -0.7980153560638428, -0.8362522125244141], [-0.8283698558807373, -0.7862045764923096, -0.7398931980133057, -0.6816996932029724, -0.6013109683990479, -0.4940323531627655, -0.37709471583366394, -0.28303489089012146, -0.23429666459560394, -0.23730693757534027, -0.2872428596019745, -0.3677229583263397, -0.4555412232875824, -0.535668134689331, -0.6042615175247192, -0.661778450012207, -0.7091243267059326, -0.7481067180633545, -0.7821465730667114, -0.8154342174530029, -0.8513507843017578], [-0.848362922668457, -0.8046026229858398, -0.7566651105880737, -0.6970747709274292, -0.6156056523323059, -0.5071984529495239, -0.3885587155818939, -0.2924287021160126, -0.2432609647512436, -0.24901674687862396, -0.3030448853969574, -0.3852780759334564, -0.471625417470932, -0.5491658449172974, -0.6161175966262817, -0.6741455793380737, -0.7245379686355591, -0.7685188055038452, -0.8079118728637695, -0.8446321487426758, -0.8803144693374634], [-0.8770304322242737, -0.831751823425293, -0.7820703387260437, -0.7211242914199829, -0.6393552422523499, -0.5319516658782959, -0.4146958291530609, -0.31928619742393494, -0.2712901532649994, -0.2795393168926239, -0.3341924846172333, -0.4126175343990326, -0.4936501681804657, -0.5676339864730835, -0.6336519718170166, -0.6934148073196411, -0.7480276823043823, -0.7979205846786499, -0.8434149026870728, -0.8847540616989136, -0.9227273464202881], [-0.9144989252090454, -0.8682724237442017, -0.8170230388641357, -0.7548134922981262, -0.6733825206756592, -0.5691190958023071, -0.4571417272090912, -0.3666524589061737, -0.3220129907131195, -0.3313034474849701, -0.38127419352531433, -0.44990190863609314, -0.5223277807235718, -0.5925766229629517, -0.6594007015228271, -0.7232067584991455, -0.7838900089263916, -0.8403773307800293, -0.8915117979049683, -0.9371147155761719, -0.978783369064331], [-0.9608165621757507, -0.9147345423698425, -0.8626974821090698, -0.799798846244812, -0.719491720199585, -0.6202425956726074, -0.5173685550689697, -0.43666741251945496, -0.3978818356990814, -0.4058017432689667, -0.4456709325313568, -0.5003794431686401, -0.5626932382583618, -0.630117654800415, -0.7003495693206787, -0.7708505392074585, -0.8386424779891968, -0.9005587100982666, -0.954858660697937, -1.0024484395980835, -1.046456217765808], [-1.0158039331436157, -0.9712592959403992, -0.9199897050857544, -0.858024537563324, -0.7805476784706116, -0.6884039044380188, -0.5977686643600464, -0.5308668613433838, -0.5002434253692627, -0.5050225257873535, -0.5320491790771484, -0.5721898078918457, -0.6245971918106079, -0.6892497539520264, -0.7625900506973267, -0.8382008075714111, -0.9095791578292847, -0.9723358154296875, -1.025613784790039, -1.0719778537750244, -1.1157742738723755]]
)


x=np.array([-2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -2.0, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.8, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.6, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.4000000000000001, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.2000000000000002, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -1.0, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.8, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.6000000000000001, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.4, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, -0.2, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.2, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.4, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.6000000000000001, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 0.8, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.0, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.2000000000000002, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.4000000000000001, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.6, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 1.8, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0, 2.0])
y=np.array([-2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0, -2.0, -1.8, -1.6, -1.4000000000000001, -1.2000000000000002, -1.0, -0.8, -0.6000000000000001, -0.4, -0.2, 0.0, 0.2, 0.4, 0.6000000000000001, 0.8, 1.0, 1.2000000000000002, 1.4000000000000001, 1.6, 1.8, 2.0])
value = np.array([[-0.9693396091461182, -0.8801171779632568, -0.8108367919921875, -0.7603106498718262, -0.7232669591903687, -0.6916850805282593, -0.6585843563079834, -0.6276825070381165, -0.6091716289520264, -0.6068120002746582, -0.6234579682350159, -0.656946063041687, -0.6887145042419434, -0.7095425128936768, -0.7258913516998291, -0.7429983615875244, -0.7620010375976562, -0.7822784185409546, -0.8042833805084229, -0.831002414226532, -0.865764856338501], [-0.8987733721733093, -0.8159864544868469, -0.7526195645332336, -0.7073512673377991, -0.6751775741577148, -0.6484270095825195, -0.619480550289154, -0.5905078649520874, -0.5726046562194824, -0.5707920789718628, -0.5880073308944702, -0.6217854022979736, -0.6526832580566406, -0.6713348627090454, -0.6842904686927795, -0.6968417167663574, -0.7108592987060547, -0.7272502779960632, -0.7478638887405396, -0.7757958173751831, -0.8134271502494812], [-0.8407058715820312, -0.763916015625, -0.7058473229408264, -0.6653012633323669, -0.6375754475593567, -0.6153086423873901, -0.5903680324554443, -0.563156008720398, -0.5453567504882812, -0.5435911417007446, -0.5610702633857727, -0.5950268507003784, -0.6249005198478699, -0.6411089897155762, -0.6502442955970764, -0.6576787233352661, -0.6664698123931885, -0.6797343492507935, -0.7006193399429321, -0.7313027381896973, -0.7725111842155457], [-0.7935147285461426, -0.721051812171936, -0.6666311025619507, -0.6296505928039551, -0.6057778596878052, -0.5878335237503052, -0.5671155452728271, -0.5420323014259338, -0.5243322849273682, -0.5224287509918213, -0.5400473475456238, -0.5742741823196411, -0.6031686067581177, -0.616765022277832, -0.6219086647033691, -0.6246705651283264, -0.629938006401062, -0.6425364017486572, -0.6655610799789429, -0.6998258233070374, -0.7448087930679321], [-0.7574135065078735, -0.6863905191421509, -0.632108211517334, -0.5956019163131714, -0.5734886527061462, -0.558996319770813, -0.5428500175476074, -0.5210750102996826, -0.504530668258667, -0.5030955076217651, -0.5211139917373657, -0.5558871030807495, -0.5839119553565979, -0.5947002172470093, -0.5962105989456177, -0.5962961912155151, -0.6012971997261047, -0.615804135799408, -0.6419249773025513, -0.6798725128173828, -0.728585958480835], [-0.7343019247055054, -0.662692129611969, -0.6046802997589111, -0.5639066696166992, -0.5390605926513672, -0.5246256589889526, -0.5116727948188782, -0.49410784244537354, -0.4806281328201294, -0.4811687469482422, -0.5001263618469238, -0.5355939865112305, -0.5628241300582886, -0.5711323022842407, -0.571041464805603, -0.5721422433853149, -0.5800824761390686, -0.5978748798370361, -0.6272150278091431, -0.6686736941337585, -0.7208231687545776], [-0.7234984636306763, -0.6513961553573608, -0.5884690284729004, -0.5401402711868286, -0.5080562829971313, -0.4884399175643921, -0.4736994504928589, -0.45766258239746094, -0.4475160837173462, -0.4514353275299072, -0.47182536125183105, -0.5079313516616821, -0.5353496670722961, -0.5438855886459351, -0.5465263724327087, -0.552299976348877, -0.5648009777069092, -0.5862035751342773, -0.6186858415603638, -0.6633174419403076, -0.7180031538009644], [-0.7210979461669922, -0.6488922834396362, -0.5823547840118408, -0.526321530342102, -0.485262393951416, -0.4574834108352661, -0.43600356578826904, -0.4149976968765259, -0.4028010368347168, -0.40771353244781494, -0.4290335178375244, -0.4668656587600708, -0.4989529848098755, -0.5135098099708557, -0.5229542851448059, -0.534868061542511, -0.5521357655525208, -0.5772119760513306, -0.6127017140388489, -0.6595945358276367, -0.7151485681533813], [-0.7220938801765442, -0.6498624682426453, -0.5804133415222168, -0.517359733581543, -0.466721773147583, -0.4297541379928589, -0.40007591247558594, -0.3702906370162964, -0.3485654592514038, -0.34623825550079346, -0.36498141288757324, -0.4078497886657715, -0.4521139860153198, -0.4778863191604614, -0.49552786350250244, -0.5134937763214111, -0.5355677008628845, -0.5644968748092651, -0.6025171279907227, -0.6502406001091003, -0.7053029537200928], [-0.7257238626480103, -0.6558520793914795, -0.5840890407562256, -0.5133451223373413, -0.450569748878479, -0.4003862142562866, -0.35885167121887207, -0.3172570466995239, -0.28193461894989014, -0.267065167427063, -0.2798212766647339, -0.3281043767929077, -0.3878237009048462, -0.42790770530700684, -0.4555472135543823, -0.4809530973434448, -0.5090676546096802, -0.5424346923828125, -0.5827034711837769, -0.6310049295425415, -0.6865605115890503], [-0.732353687286377, -0.6680165529251099, -0.5986042618751526, -0.5236407518386841, -0.44919323921203613, -0.38263630867004395, -0.3242682218551636, -0.26628220081329346, -0.21463358402252197, -0.18789517879486084, -0.19480633735656738, -0.24310088157653809, -0.31279098987579346, -0.3678549528121948, -0.4096883535385132, -0.44652414321899414, -0.4828023910522461, -0.5209567546844482, -0.5635086297988892, -0.6134895086288452, -0.6717706918716431], [-0.7382638454437256, -0.6790344715118408, -0.6161797642707825, -0.5458967685699463, -0.4700329303741455, -0.3939850330352783, -0.3205677270889282, -0.24624478816986084, -0.18024146556854248, -0.14473199844360352, -0.14509999752044678, -0.1860595941543579, -0.2530043125152588, -0.3130166530609131, -0.3659360408782959, -0.41475653648376465, -0.45984113216400146, -0.5033895969390869, -0.549915075302124, -0.6042559146881104, -0.6678622961044312], [-0.7439861297607422, -0.6888316869735718, -0.6333849430084229, -0.5724977254867554, -0.5047588348388672, -0.4309213161468506, -0.3503032922744751, -0.26269662380218506, -0.18444883823394775, -0.14114868640899658, -0.13525187969207764, -0.16863596439361572, -0.22796034812927246, -0.2822556495666504, -0.3340102434158325, -0.3855327367782593, -0.4343423843383789, -0.4821721315383911, -0.5345494747161865, -0.5963572263717651, -0.6678907871246338], [-0.7550859451293945, -0.7035195827484131, -0.6542675495147705, -0.601350724697113, -0.5415623188018799, -0.47383618354797363, -0.39541149139404297, -0.30557334423065186, -0.22104156017303467, -0.16924118995666504, -0.155889630317688, -0.18328261375427246, -0.23660600185394287, -0.28313958644866943, -0.3264347314834595, -0.3722459077835083, -0.4196140766143799, -0.4691445827484131, -0.5251615047454834, -0.5916053056716919, -0.6679478883743286], [-0.7750188112258911, -0.7256902456283569, -0.6803935170173645, -0.6326950788497925, -0.5777056217193604, -0.5134091377258301, -0.4394714832305908, -0.35762155055999756, -0.27829277515411377, -0.2241271734237671, -0.20512330532073975, -0.22649526596069336, -0.2721734046936035, -0.3086264133453369, -0.3410545587539673, -0.37889397144317627, -0.42403292655944824, -0.475909948348999, -0.5354655981063843, -0.6037983894348145, -0.6793814897537231], [-0.8032724857330322, -0.7547563314437866, -0.7113863229751587, -0.6668450832366943, -0.6148086786270142, -0.5521749258041382, -0.4823116064071655, -0.41087114810943604, -0.34229791164398193, -0.2933894395828247, -0.275989294052124, -0.29455339908599854, -0.33070623874664307, -0.35488438606262207, -0.3764759302139282, -0.40652549266815186, -0.4481539726257324, -0.5009510517120361, -0.5637602806091309, -0.6339111328125, -0.707084059715271], [-0.8380391597747803, -0.789940595626831, -0.74726402759552, -0.7044547200202942, -0.6547496318817139, -0.594882607460022, -0.5307698249816895, -0.4692307710647583, -0.4103732109069824, -0.3667052984237671, -0.35244596004486084, -0.3719538450241089, -0.4035118818283081, -0.42070603370666504, -0.43509209156036377, -0.4578288793563843, -0.4932734966278076, -0.5430169105529785, -0.6055740118026733, -0.6744552850723267, -0.7428025007247925], [-0.8780637979507446, -0.8312147855758667, -0.7891132831573486, -0.7472847700119019, -0.6999979019165039, -0.6455408930778503, -0.5897877216339111, -0.536503791809082, -0.48303890228271484, -0.4400843381881714, -0.42636191844940186, -0.4478602409362793, -0.47965383529663086, -0.49751222133636475, -0.5114096403121948, -0.5303771495819092, -0.559801459312439, -0.6032946109771729, -0.6596469879150391, -0.721369743347168, -0.7821807861328125], [-0.9244278073310852, -0.8802056312561035, -0.8395999670028687, -0.7987861633300781, -0.7541112899780273, -0.7063811421394348, -0.6591713428497314, -0.6115380525588989, -0.5599044561386108, -0.5149868726730347, -0.5007821321487427, -0.5248538255691528, -0.5576447248458862, -0.5771971940994263, -0.5931128263473511, -0.6131638288497925, -0.6411207914352417, -0.6788331270217896, -0.7255969047546387, -0.7767788171768188, -0.8289562463760376], [-0.9820675849914551, -0.9413673877716064, -0.9030877351760864, -0.8633398413658142, -0.8206771612167358, -0.7781949043273926, -0.7373519539833069, -0.6943734884262085, -0.6454242467880249, -0.600406289100647, -0.5863338708877563, -0.6111443042755127, -0.6415500640869141, -0.6598657369613647, -0.6767855882644653, -0.6989148855209351, -0.7270010709762573, -0.760886549949646, -0.8007103204727173, -0.8438501358032227, -0.8888550996780396], [-1.0549429655075073, -1.0172317028045654, -0.9805482625961304, -0.9413827657699585, -0.9005316495895386, -0.8622474670410156, -0.8268524408340454, -0.7899229526519775, -0.7470723390579224, -0.7054203748703003, -0.6917507648468018, -0.713533878326416, -0.737011194229126, -0.7511458396911621, -0.7672113180160522, -0.7887042760848999, -0.8138699531555176, -0.8436121940612793, -0.8796828985214233, -0.9195690155029297, -0.9614615440368652]]).flatten()


def plot_v_value_2d(x, y, value, width=480, height=480, show=False):
    import matplotlib
    if not show:
        matplotlib.use('agg')
    import matplotlib.pyplot as plt

    value = value.reshape((21, 21))
    max_value = np.max(value)
    max_index = np.argmax(value)
    max_index_2d = np.unravel_index(max_index, value.shape)
    max_x_index, max_y_index = max_index_2d

    max_x = (-10 + max_x_index) * 0.2
    max_y = (-10 + max_y_index) * 0.2

    text = "max value={:.3f}, x={:.2f}, y={:.2f}".format(max_value, max_x, max_y)

    if not show:
        plt.ioff()

    fig, ax = plt.subplots(figsize=(width / 100, height / 100))
    sc = ax.scatter(x, y, c=value, cmap='viridis')
    cbar = fig.colorbar(sc, ax=ax)
    plt.title(text)

    fig.canvas.draw()
    img_array = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
    img_array = img_array.reshape((width, height, 3))
    # img = fig.to_image(format="png", width=width, height=height)

    if show:
        plt.show()

    plt.close()
    return img_array


if __name__ == '__main__':
    plot_v_value_2d(x, y, value, show=True)
